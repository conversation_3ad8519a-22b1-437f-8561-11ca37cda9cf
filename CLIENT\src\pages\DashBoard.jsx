import React from "react";
import { <PERSON>, <PERSON>rid, Card, CardContent, Ty<PERSON><PERSON>, Divider, Stack, Container, IconButton } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import api from "../config/api";
import DashboardHeader from "../global/components/DashboardHeader";
import AddCircleOutlineIcon from "@mui/icons-material/AddCircleOutline";
import ListAltIcon from "@mui/icons-material/ListAlt";
import AssessmentIcon from "@mui/icons-material/Assessment";
import TrendingUpIcon from "@mui/icons-material/TrendingUp";
import Brightness4Icon from "@mui/icons-material/Brightness4";
import Brightness7Icon from "@mui/icons-material/Brightness7";
import { useNavigate } from "react-router-dom";
import { useThemeContext } from "../context/ThemeContext";

const HomePage = () => {
  const navigate = useNavigate();
  const { mode, toggleTheme } = useThemeContext();
  const isDarkMode = mode === 'dark';
  
  const { data: stats, isLoading } = useQuery({
    queryKey: ["dashboardStats"],
    queryFn: async () => {
      const res = await api.get("/stats/overview");
      return res.data;
    },
  });

  // Fallback values if data is not yet loaded
  const totalProposals = stats?.totalProposals || 0;
  const pendingProposals = stats?.pendingProposals || 0;
  const approvedProposals = stats?.approvedProposals || 0;
  const rejectedProposals = stats?.rejectedProposals || 0;

  const handleCreateProposal = () => {
    navigate("/proposals/create");
  };

  const handleViewProposals = () => {
    navigate("/proposals");
  };

  const handleGenerateReports = () => {
    navigate("/reports");
  };

  return (
    <>
      <DashboardHeader 
        title="Dashboard" 
        description="Overview of your budget proposals and system status"
        action={
          <IconButton onClick={toggleTheme} color="inherit" sx={{ ml: 1 }}>
            {isDarkMode ? <Brightness7Icon /> : <Brightness4Icon />}
          </IconButton>
        }
      />
      
      <Container maxWidth="xl">
        {/* Welcome Card */}
        <Card 
          elevation={3} 
          sx={{ 
            mt: 3, 
            mb: 4, 
            borderRadius: 2,
            background: isDarkMode 
              ? "linear-gradient(to right, #1a237e, #283593)" 
              : "linear-gradient(to right, rgba(38, 69, 36, 0.9), rgba(55, 94, 56, 0.7))",
            color: "white"
          }}
        >
          <CardContent sx={{ py: 3 }}>
            <Typography variant="h4" fontWeight="bold" gutterBottom>
              Welcome to the Budget Proposal System
            </Typography>
            <Typography variant="body1">
              Manage and track all your budget proposals in one place. Create new proposals, monitor their status, and generate reports.
            </Typography>
          </CardContent>
        </Card>

        {/* Stats Cards */}
        <Typography variant="h5" fontWeight="bold" sx={{ mb: 2 }} >
          Proposal Statistics
        </Typography>
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card 
              elevation={3} 
              sx={{ 
                height: '100%', 
                borderRadius: 2,
                transition: 'transform 0.3s',
                '&:hover': { transform: 'translateY(-5px)' }
              }}
            >
              <CardContent sx={{ textAlign: "center", p: 3 }}>
                <TrendingUpIcon sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Total Proposals
                </Typography>
                <Typography variant="h3" color="primary.main" fontWeight="bold">
                  {isLoading ? "..." : totalProposals}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card 
              elevation={3} 
              sx={{ 
                height: '100%', 
                borderRadius: 2,
                transition: 'transform 0.3s',
                '&:hover': { transform: 'translateY(-5px)' }
              }}
            >
              <CardContent sx={{ textAlign: "center", p: 3 }}>
                <Box sx={{ fontSize: 40, color: 'warning.main', mb: 1 }}>⌛</Box>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Pending
                </Typography>
                <Typography variant="h3" color="warning.main" fontWeight="bold">
                  {isLoading ? "..." : pendingProposals}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card 
              elevation={3} 
              sx={{ 
                height: '100%', 
                borderRadius: 2,
                transition: 'transform 0.3s',
                '&:hover': { transform: 'translateY(-5px)' }
              }}
            >
              <CardContent sx={{ textAlign: "center", p: 3 }}>
                <Box sx={{ fontSize: 40, color: 'success.main', mb: 1 }}>✓</Box>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Approved
                </Typography>
                <Typography variant="h3" color="success.main" fontWeight="bold">
                  {isLoading ? "..." : approvedProposals}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={3}>
            <Card 
              elevation={3} 
              sx={{ 
                height: '100%', 
                borderRadius: 2,
                transition: 'transform 0.3s',
                '&:hover': { transform: 'translateY(-5px)' }
              }}
            >
              <CardContent sx={{ textAlign: "center", p: 3 }}>
                <Box sx={{ fontSize: 40, color: 'error.main', mb: 1 }}>✗</Box>
                <Typography variant="h6" fontWeight="bold" gutterBottom>
                  Rejected
                </Typography>
                <Typography variant="h3" color="error.main" fontWeight="bold">
                  {isLoading ? "..." : rejectedProposals}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Quick Actions */}
        <Typography variant="h5" fontWeight="bold" sx={{ mb: 2 }}>
          Quick Actions
        </Typography>
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={4}>
            <Card 
              elevation={3} 
              sx={{ 
                borderRadius: 2,
                cursor: 'pointer',
                transition: 'transform 0.3s',
                '&:hover': { transform: 'translateY(-5px)' },
                height: '100%'
              }}
              onClick={handleCreateProposal}
            >
              <CardContent sx={{ p: 3 }}>
                <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 2 }}>
                  <AddCircleOutlineIcon sx={{ fontSize: 40, color: 'primary.main' }} />
                  <Typography variant="h6" fontWeight="bold">
                    Create New Proposal
                  </Typography>
                </Stack>
                <Typography variant="body2" color="text.secondary">
                  Start a new budget proposal submission with our step-by-step wizard.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card 
              elevation={3} 
              sx={{ 
                borderRadius: 2,
                cursor: 'pointer',
                transition: 'transform 0.3s',
                '&:hover': { transform: 'translateY(-5px)' },
                height: '100%'
              }}
              onClick={handleViewProposals}
            >
              <CardContent sx={{ p: 3 }}>
                <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 2 }}>
                  <ListAltIcon sx={{ fontSize: 40, color: 'secondary.main' }} />
                  <Typography variant="h6" fontWeight="bold">
                    View All Proposals
                  </Typography>
                </Stack>
                <Typography variant="body2" color="text.secondary">
                  Browse, filter, and manage all your submitted budget proposals.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} md={4}>
            <Card 
              elevation={3} 
              sx={{ 
                borderRadius: 2,
                cursor: 'pointer',
                transition: 'transform 0.3s',
                '&:hover': { transform: 'translateY(-5px)' },
                height: '100%'
              }}
              onClick={handleGenerateReports}
            >
              <CardContent sx={{ p: 3 }}>
                <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 2 }}>
                  <AssessmentIcon sx={{ fontSize: 40, color: 'success.main' }} />
                  <Typography variant="h6" fontWeight="bold">
                    Generate Reports
                  </Typography>
                </Stack>
                <Typography variant="body2" color="text.secondary">
                  Create customized reports and export data for analysis.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Announcements */}
        <Typography variant="h5" fontWeight="bold" sx={{ mb: 2 }}>
          Announcements & Deadlines
        </Typography>
        <Card elevation={3} sx={{ borderRadius: 2, mb: 4 }}>
          <CardContent sx={{ p: 3 }}>
            <Typography variant="h6" color="primary" gutterBottom>
              Important Dates
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            <Stack spacing={2}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box 
                  sx={{ 
                    width: 8, 
                    height: 8, 
                    borderRadius: '50%', 
                    bgcolor: 'error.main', 
                    mr: 2 
                  }} 
                />
                <Typography variant="body1" fontWeight="medium">
                  Submission Deadline: <Box component="span" fontWeight="bold">{stats?.settings?.submissionDeadline || "TBD"}</Box>
                </Typography>
              </Box>
              
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box 
                  sx={{ 
                    width: 8, 
                    height: 8, 
                    borderRadius: '50%', 
                    bgcolor: 'warning.main', 
                    mr: 2 
                  }} 
                />
                <Typography variant="body1" fontWeight="medium">
                  System Maintenance: <Box component="span" fontWeight="bold">October 10, 2025</Box>
                </Typography>
              </Box>
              
              {stats?.settings?.additionalAnnouncements?.map((announcement, index) => (
                <Box key={index} sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box 
                    sx={{ 
                      width: 8, 
                      height: 8, 
                      borderRadius: '50%', 
                      bgcolor: 'info.main', 
                      mr: 2 
                    }} 
                  />
                  <Typography variant="body1" fontWeight="medium">
                    {announcement}
                  </Typography>
                </Box>
              ))}
            </Stack>
          </CardContent>
        </Card>
      </Container>
    </>
  );
};

export default HomePage;
