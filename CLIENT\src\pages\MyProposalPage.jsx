import React, { useState, useEffect, useRef } from "react";
import { 
  Box, 
  Paper, 
  Typography, 
  Button,
  CircularProgress,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  Alert,
  Chip,
  IconButton,
  Tooltip,
  Divider
} from "@mui/material";
import { 
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Add as AddIcon
} from "@mui/icons-material";
import { useNavigate } from "react-router-dom";
import { blueGrey, green, grey } from "@mui/material/colors";
import api from "../config/api";
import { useUser } from "../context/UserContext";
import DashboardHeader from "../global/components/DashboardHeader";
import StyledBox from "../global/components/StyledBox";
import formatCurrency from "../utils/formatCurrency";
import { formatDateToMDY } from "../utils/formatDate";
import ProposalViewModal from "../components/myproposal/ProposalViewModal";
import { toast } from "react-toastify";

const MyProposalPage = () => {
  const navigate = useNavigate();
  const { currentUser } = useUser();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);
  const [activeTab, setActiveTab] = useState(0);
  const [selectedProposal, setSelectedProposal] = useState(null);
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  
  // Pagination states for each table
  const [paginationState, setPaginationState] = useState({
    personnel: { page: 0, rowsPerPage: 5 },
    mooe: { page: 0, rowsPerPage: 5 },
    capitalOutlay: { page: 0, rowsPerPage: 5 },
    income: { page: 0, rowsPerPage: 5 },
    draft: { page: 0, rowsPerPage: 5 },
    returned: { page: 0, rowsPerPage: 5 },
    approved: { page: 0, rowsPerPage: 5 }
  });

  // Function to handle page change for a specific table
  const handleChangePage = (tableType, newPage) => {
    setPaginationState(prev => ({
      ...prev,
      [tableType]: { ...prev[tableType], page: newPage }
    }));
  };

  // Function to handle rows per page change for a specific table
  const handleChangeRowsPerPage = (tableType, event) => {
    setPaginationState(prev => ({
      ...prev,
      [tableType]: { 
        page: 0, // Reset to first page
        rowsPerPage: parseInt(event.target.value, 10) 
      }
    }));
  };

  const fetchProposals = async () => {
    setLoading(true);
    setError(null);
    
    try {
      console.log("Fetching all user proposals...");
      const response = await api.get("/myproposals/all", {
        params: {
          processBy: `${currentUser.FirstName} ${currentUser.LastName}`
        }
      });
      console.log("API Response from /myproposals/all:", response.data);
      setData(response.data);
    } catch (err) {
      console.error("Error fetching proposals:", err);
      setError("Failed to fetch proposals. Please try again later.");
      toast.error("Failed to load proposals. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (currentUser?.FirstName && currentUser?.LastName) {
      fetchProposals();
    }
  }, [currentUser?.FirstName, currentUser?.LastName, refreshTrigger]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleViewProposal = (proposal) => {
    // Enhance proposal with user information if missing
    const enhancedProposal = {
      ...proposal,
      processBy: proposal.processBy || `${currentUser.FirstName} ${currentUser.LastName}`
    };
    
    setSelectedProposal(enhancedProposal);
    setViewModalOpen(true);
  };

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleCreateProposal = () => {
    navigate("/proposals/create");
  };

  // Helper function to get proposals by status
  const getProposalsByStatus = (category, status) => {
    if (!data || !data[category]) return [];
    return data[category].filter(item => item.status === status);
  };

  // Get all proposals for a specific status across all categories
  const getAllProposalsByStatus = (status) => {
    if (!data) return {};
    
    const result = {};
    ['personnel', 'mooe', 'capitalOutlay', 'income'].forEach(category => {
      if (data[category]) {
        const filtered = data[category].filter(item => item.status === status);
        if (filtered.length > 0) {
          result[category] = filtered;
        }
      }
    });
    
    return result;
  };

  // Get status color for chips
  const getStatusColor = (status) => {
    switch (status) {
      case "Draft": return "default";
      case "Submitted": return "primary";
      case "Returned": return "error";
      case "Approved": return "success";
      default: return "default";
    }
  };

  // Get formatted date with fallbacks
  const getFormattedDate = (proposal) => {
    const dateValue = proposal.updatedAt || proposal.submittedDate || 
                      proposal.rejectedDate || proposal.approvedDate;
    
    if (!dateValue) return "N/A";
    
    try {
      return new Date(dateValue).toLocaleDateString();
    } catch (err) {
      console.warn("Invalid date format:", dateValue);
      return "Invalid date";
    }
  };

  // Render the proposals table for a specific category and status with pagination
  const renderProposalsTable = (category, proposals, tableType) => {
    const { page, rowsPerPage } = paginationState[tableType];
    const paginatedProposals = proposals.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
    
    // Format category name for display
    const getCategoryDisplayName = (cat) => {
      switch(cat.toLowerCase()) {
        case 'personnel': return 'Personnel Services';
        case 'mooe': return 'MOOE';
        case 'capitaloutlay': return 'Capital Outlay';
        case 'income': return 'Income';
        default: return cat.charAt(0).toUpperCase() + cat.slice(1);
      }
    };
    
    return (
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" gutterBottom sx={{ 
          color: '#375e38', 
          fontWeight: 600,
          display: 'flex',
          alignItems: 'center',
          gap: 1
        }}>
          {getCategoryDisplayName(category)}
          <Chip 
            label={`${proposals.length} proposal${proposals.length !== 1 ? 's' : ''}`} 
            size="small" 
            sx={{ ml: 1 }}
          />
        </Typography>
        <TableContainer component={Paper} variant="outlined" sx={{ boxShadow: '0 2px 4px rgba(0,0,0,0.05)' }}>
          <Table size="small">
            <TableHead sx={{ backgroundColor: '#f5f5f5' }}>
              <TableRow>
                <TableCell><strong>Fiscal Year</strong></TableCell>
                <TableCell><strong>Budget Type</strong></TableCell>
                <TableCell><strong>Region</strong></TableCell>
                <TableCell><strong>Status</strong></TableCell>
                <TableCell><strong>Last Updated</strong></TableCell>
                <TableCell align="center"><strong>Actions</strong></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedProposals.length > 0 ? (
                paginatedProposals.map((proposal, index) => (
                  <TableRow 
                    key={proposal._id || index} 
                    sx={{ 
                      '&:hover': { backgroundColor: '#f9f9f9' },
                      backgroundColor: index % 2 === 0 ? 'white' : '#fafafa'
                    }}
                  >
                    <TableCell>{proposal.fiscalYear || 'N/A'}</TableCell>
                    <TableCell>{proposal.budgetType || 'N/A'}</TableCell>
                    <TableCell>{proposal.region || 'N/A'}</TableCell>
                    <TableCell>
                      <Chip 
                        label={proposal.status} 
                        size="small"
                        color={getStatusColor(proposal.status)}
                        sx={{ fontWeight: 500 }}
                      />
                      {proposal.status === "Returned" && proposal.returnedExpenditureType && (
                        <Typography variant="caption" display="block" color="error" sx={{ mt: 0.5 }}>
                          {proposal.returnedExpenditureType} returned
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>{getFormattedDate(proposal)}</TableCell>
                    <TableCell align="center">
                      <Button 
                        size="small" 
                        variant="outlined" 
                        onClick={() => handleViewProposal(proposal)}
                        sx={{ 
                          minWidth: '100px',
                          borderRadius: '4px',
                          textTransform: 'none'
                        }}
                      >
                        View Details
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 3 }}>
                    <Typography color="textSecondary">No proposals found</Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        
        {/* Add pagination control */}
        {proposals.length > 0 && (
          <TablePagination
            component="div"
            count={proposals.length}
            page={page}
            onPageChange={(event, newPage) => handleChangePage(tableType, newPage)}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={(event) => handleChangeRowsPerPage(tableType, event)}
            rowsPerPageOptions={[5, 10, 25]}
            sx={{ mt: 1 }}
          />
        )}
      </Box>
    );
  };

  // Render empty state when no data is available
  const renderEmptyState = (message) => (
    <Box 
      sx={{ 
        display: 'flex', 
        flexDirection: 'column',
        alignItems: 'center', 
        justifyContent: 'center',
        p: 5,
        backgroundColor: '#f9f9f9',
        borderRadius: 2,
        my: 3
      }}
    >
      <Typography variant="h6" color="textSecondary" gutterBottom>
        {message}
      </Typography>
      <Button 
        variant="contained" 
        color="primary" 
        startIcon={<AddIcon />}
        onClick={handleCreateProposal}
        sx={{ mt: 2 }}
      >
        Create New Proposal
      </Button>
    </Box>
  );

  if (loading && !data) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>
        <Button 
          variant="outlined" 
          startIcon={<RefreshIcon />} 
          onClick={handleRefresh}
        >
          Try Again
        </Button>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        mb: 3
      }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 600, color: '#375e38' }}>
          My Proposals
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Tooltip title="Refresh proposals">
            <IconButton onClick={handleRefresh} disabled={loading}>
              {loading ? <CircularProgress size={24} /> : <RefreshIcon />}
            </IconButton>
          </Tooltip>
          <Button 
            variant="contained" 
            color="primary" 
            startIcon={<AddIcon />}
            onClick={handleCreateProposal}
          >
            Create New Proposal
          </Button>
        </Box>
      </Box>
      
      <Tabs 
        value={activeTab} 
        onChange={handleTabChange} 
        sx={{ 
          mb: 3,
          '& .MuiTab-root': {
            fontWeight: 600,
            textTransform: 'none',
            minWidth: 100
          },
          '& .Mui-selected': {
            color: '#375e38',
          },
          '& .MuiTabs-indicator': {
            backgroundColor: '#375e38',
          }
        }}
      >
        <Tab label="Submitted" />
        <Tab label="Draft" />
        <Tab label="Returned" />
        <Tab label="Approved" />
      </Tabs>
      
      <Divider sx={{ mb: 3 }} />
      
      {activeTab === 0 && (
        <Box>
          <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
            Submitted Proposals
          </Typography>
          
          {/* Personnel Proposals */}
          {data?.personnel && data.personnel.filter(p => p.status === "Submitted").length > 0 && (
            renderProposalsTable("Personnel", data.personnel.filter(p => p.status === "Submitted"), "personnel")
          )}
          
          {/* MOOE Proposals */}
          {data?.mooe && data.mooe.filter(p => p.status === "Submitted").length > 0 && (
            renderProposalsTable("MOOE", data.mooe.filter(p => p.status === "Submitted"), "mooe")
          )}
          
          {/* Capital Outlay Proposals */}
          {data?.capitalOutlay && data.capitalOutlay.filter(p => p.status === "Submitted").length > 0 && (
            renderProposalsTable("Capital Outlay", data.capitalOutlay.filter(p => p.status === "Submitted"), "capitalOutlay")
          )}
          
          {/* Income Proposals */}
          {data?.income && data.income.filter(p => p.status === "Submitted").length > 0 && (
            renderProposalsTable("Income", data.income.filter(p => p.status === "Submitted"), "income")
          )}
          
          {/* No submitted proposals message */}
          {(!data?.personnel || data.personnel.filter(p => p.status === "Submitted").length === 0) &&
           (!data?.mooe || data.mooe.filter(p => p.status === "Submitted").length === 0) &&
           (!data?.capitalOutlay || data.capitalOutlay.filter(p => p.status === "Submitted").length === 0) &&
           (!data?.income || data.income.filter(p => p.status === "Submitted").length === 0) && (
            renderEmptyState("No submitted proposals found")
          )}
        </Box>
      )}
      
      {activeTab === 1 && (
        <Box>
          <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
            Draft Proposals
          </Typography>
          
          {Object.entries(getAllProposalsByStatus("Draft")).map(([category, proposals]) => (
            <React.Fragment key={category}>
              {renderProposalsTable(category, proposals, "draft")}
            </React.Fragment>
          ))}
          
          {Object.keys(getAllProposalsByStatus("Draft")).length === 0 && (
            renderEmptyState("No draft proposals found")
          )}
        </Box>
      )}
      
      {activeTab === 2 && (
        <Box>
          <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
            Returned Proposals
          </Typography>
          
          {Object.entries(getAllProposalsByStatus("Returned")).map(([category, proposals]) => (
            <React.Fragment key={category}>
              {renderProposalsTable(category, proposals, "returned")}
            </React.Fragment>
          ))}
          
          {Object.keys(getAllProposalsByStatus("Returned")).length === 0 && (
            renderEmptyState("No returned proposals found")
          )}
        </Box>
      )}
      
      {activeTab === 3 && (
        <Box>
          <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
            Approved Proposals
          </Typography>
          
          {Object.entries(getAllProposalsByStatus("Approved")).map(([category, proposals]) => (
            <React.Fragment key={category}>
              {renderProposalsTable(category, proposals, "approved")}
            </React.Fragment>
          ))}
          
          {Object.keys(getAllProposalsByStatus("Approved")).length === 0 && (
            renderEmptyState("No approved proposals found")
          )}
        </Box>
      )}
      
      {/* Proposal View Modal */}
      <ProposalViewModal 
        open={viewModalOpen}
        onClose={() => setViewModalOpen(false)}
        proposal={selectedProposal}
        onRefresh={handleRefresh}
      />
    </Box>
  );
};

export default MyProposalPage;
