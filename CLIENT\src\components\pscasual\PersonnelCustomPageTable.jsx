import React, { useState, useEffect, useCallback, useMemo } from "react";
import CustomMenu from "./PersonnelCustomMenu";
import CustomTable from "./PersonnelCustomTable";
import PropTypes from "prop-types";
import {
  Button,
  Box,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  CircularProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import api from "../../config/api";
import { useUser } from "../../context/UserContext";
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import { useQueryClient } from "@tanstack/react-query";
import VisibilityIcon from '@mui/icons-material/Visibility';

// Helper function to format currency
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-PH', {
    style: 'decimal',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

const PSCasualCustomPageTable = ({
  dataListName,
  schema,
  title = "",
  description = "",
  searchable = true,
  hasEdit = true,
  hasDelete = true,
  hasAdd = true,
  customAddElement = null,
  customEditElement = null,
  additionalMenuOptions = [],
  ROWS_PER_PAGE = 20,
}) => {
  const [rows, setRows] = useState([]);
  const [filteredRows, setFilteredRows] = useState([]);
  const [openDialog, setOpenDialog] = useState(false);
  const [loading, setLoading] = useState(false);
  const [fiscalYear, setFiscalYear] = useState("");
  const [budgetType, setBudgetType] = useState("");
  const [PERA, setPERA] = useState(0);
  const [RATA, setRATA] = useState([]);
  const [compensation, setCompensation] = useState({});
  const { currentUser } = useUser();
  const queryClient = useQueryClient();

  // New state for view details
  const [selectedPersonnel, setSelectedPersonnel] = useState(null);
  const [openViewDialog, setOpenViewDialog] = useState(false);
  const [viewData, setViewData] = useState([]);
  const [grandTotal, setGrandTotal] = useState(0);

  const apiPath = `/${dataListName}`;

  const pageTitle = useMemo(() => title || dataListName.charAt(0).toUpperCase() + dataListName.slice(1), [title, dataListName]);
  const pageDescription = useMemo(() => description || `Manage ${dataListName}`, [description, dataListName]);

  const fetchActiveSettings = useCallback(async () => {
    try {
      const response = await api.get("/settings/active");
      if (response.data) {
        setFiscalYear(response.data.fiscalYear || "");
        setBudgetType(response.data.budgetType || "");
        setPERA(response.data.PERA || 0);
        setCompensation({
          uniformALLOWANCE: response.data.uniformAllowance,
          productivityIncentive: response.data.productivityIncentive,
          medicalAllowance: response.data.medicalAllowance,
          meal: response.data.meal,
          cashGift: response.data.cashGift,
          pagibigPremium: response.data.pagibigPremium,
          gsisPremium: response.data.gsisPremium,
          employeeCompensation: response.data.employeeCompensation,
        });
      } else {
        toast.error("Active settings not found.");
      }
    } catch (error) {
      toast.error("Error fetching active settings.");
      console.error("Error fetching active settings:", error);
    }
  }, []);

  const fetchData = useCallback(async () => {
    try {
      const { data } = await api.get(`${apiPath}?statusOfAppointment=CASUAL`);
      if (data && Array.isArray(data.personnelServices)) {
        setRows(data.personnelServices);
        setFilteredRows(data.personnelServices);
      } else {
        toast.error("Fetched data is not in the expected format.");
        console.error("Fetched data is not an array:", data);
      }
    } catch (error) {
      toast.error("Error fetching personnel data.");
      console.error("Error fetching data:", error);
    }
  }, [apiPath]);

  const fetchRATA = useCallback(async () => {
    try {
      const response = await api.get("/ratas");
      if (response.data && Array.isArray(response.data.ratas)) {
        setRATA(response.data.ratas);
      } else {
        toast.error("RATA data format incorrect.");
        console.error("RATA response format incorrect:", response.data);
      }
    } catch (error) {
      toast.error("Error fetching RATA data.");
      console.error("Error fetching RATA data:", error);
    }
  }, []);

  useEffect(() => {
    fetchData();
    fetchActiveSettings();
    fetchRATA();
  }, [fetchData, fetchActiveSettings, fetchRATA]);

  useEffect(() => {
    setFilteredRows(rows);
  }, [rows]);

  const handleAddAllPersonnel = useCallback(async () => {
    if (!fiscalYear) {
      toast.error("Active fiscal year not set. Please try again later.");
      return;
    }

    setLoading(true);
    try {
      const processBy = `${currentUser.FirstName} ${currentUser.LastName}`;
      const response = await api.post(`/personnelServices/bulk-add`, {
        processBy,
        statusOfAppointment: "CASUAL",
        fiscalYear,
        budgetType,
        PERA,
        RATA: RATA.map(item => ({ grade: item.SG, amount: item.RATA })),
        compensation,
      });

      if (Array.isArray(response.data)) {
        toast.success("Casual personnel successfully added!");
        await fetchData();
        queryClient.invalidateQueries(dataListName);
      } else {
        toast.info(response.data.message || "Bulk add failed");
        console.error("Bulk add response is not an array:", response.data);
      }
    } catch (error) {
      if (error.response?.status === 403) {
        toast.error(error.response.data.message || "Submission is locked.");
      } else {
        toast.error("Error bulk adding personnel.");
      }
      console.error("Error bulk adding personnel:", error);
    } finally {
      setLoading(false);
      setOpenDialog(false);
    }
  }, [fiscalYear, budgetType, PERA, RATA, compensation, currentUser, fetchData, queryClient, dataListName]);

  const handleOpenDialog = () => setOpenDialog(true);
  const handleCloseDialog = () => setOpenDialog(false);

  // New function to handle view details
  const handleViewDetails = useCallback(async (personnelId) => {
    try {
      const { data } = await api.get(`${apiPath}/${personnelId}`);
      if (data) {
        setSelectedPersonnel(data);
        
        // Format the data for the view table
        const formattedData = [{
          positionTitle: data.positionTitle || 'N/A',
          gradelevel_SG: data.gradelevel_JG || 'N/A',
          step: data.step || 'N/A',
          employeeFullName: data.employeeFullName || 'N/A',
          division: data.division || 'N/A',
          monthlySalary: data.monthlySalary || 0,
          annualSalary: data.annualSalary || 0,
          RATA: data.RATA || 0,
          PERA: data.PERA || 0,
          uniformALLOWANCE: data.uniformALLOWANCE || 0,
          midyearBonus: data.midyearBonus || 0,
          yearEndBonus: data.yearEndBonus || 0,
          Total: data.Total || 0,
        }];
        
        setViewData(formattedData);
        setGrandTotal(data.Total || 0);
        setOpenViewDialog(true);
      }
    } catch (error) {
      toast.error("Error fetching personnel details.");
      console.error("Error fetching personnel details:", error);
    }
  }, [apiPath]);

  // Function to close view dialog
  const handleCloseViewDialog = () => {
    setOpenViewDialog(false);
    setSelectedPersonnel(null);
  };

  // Add the view details function to the menu options
  const menuOptions = useMemo(() => {
    const options = [...additionalMenuOptions];
    
    // Add view details option
    options.push((props) => (
      <MenuItem 
        key="view-details" 
        onClick={() => handleViewDetails(props.row._id)}
        sx={{ display: "flex", gap: 1 }}
      >
        <VisibilityIcon fontSize="small" />
        View Details
      </MenuItem>
    ));
    
    return options;
  }, [additionalMenuOptions, handleViewDetails]);

  return (
    <>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Box display="flex" alignItems="center">
          <Button
            variant="contained"
            color="primary"
            onClick={handleOpenDialog}
            sx={{
              mr: 2,
              background: "#009688",
              color: "#fff",
              "&:hover": {
                background: "#00796B",
                color: "#fff",
              },
            }}
            startIcon={<PersonAddIcon />}
          >
            Add Casual Personnel
          </Button>
        </Box>
      </Box>

      <Dialog open={openDialog} onClose={handleCloseDialog}>
        <DialogTitle>Confirm Bulk Add</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to add all personnel with a status of appointment as CASUAL?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} color="primary">
            Cancel
          </Button>
          <Button
            onClick={handleAddAllPersonnel}
            color="primary"
            autoFocus
          >
            {loading ? <CircularProgress size={24} /> : "Yes"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* New View Details Dialog */}
      <Dialog
        open={openViewDialog}
        onClose={handleCloseViewDialog}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>Personnel Details</DialogTitle>
        <DialogContent>
          {selectedPersonnel && (
            <>
              <Typography variant="h6" gutterBottom>
                {selectedPersonnel.employeeFullName} - {selectedPersonnel.positionTitle}
              </Typography>
              
              <TableContainer component={Paper} variant="outlined" sx={{ mb: 3, maxHeight: 600, overflow: 'auto' }}>
                <Table size="small" stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ color: 'white', backgroundColor: '#375e38' }}><strong>Position Title</strong></TableCell>
                      <TableCell sx={{ color: 'white', backgroundColor: '#375e38' }}><strong>SG</strong></TableCell>
                      <TableCell sx={{ color: 'white', backgroundColor: '#375e38' }}><strong>Step</strong></TableCell>
                      <TableCell sx={{ color: 'white', backgroundColor: '#375e38' }}><strong>Employee Name</strong></TableCell>
                      <TableCell sx={{ color: 'white', backgroundColor: '#375e38' }}><strong>Division</strong></TableCell>
                      <TableCell sx={{ color: 'white', backgroundColor: '#375e38' }} align="right"><strong>Monthly</strong></TableCell>
                      <TableCell sx={{ color: 'white', backgroundColor: '#375e38' }} align="right"><strong>Annual</strong></TableCell>
                      <TableCell sx={{ color: 'white', backgroundColor: '#375e38' }} align="right"><strong>RATA</strong></TableCell>
                      <TableCell sx={{ color: 'white', backgroundColor: '#375e38' }} align="right"><strong>PERA</strong></TableCell>
                      <TableCell sx={{ color: 'white', backgroundColor: '#375e38' }} align="right"><strong>Uniform</strong></TableCell>
                      <TableCell sx={{ color: 'white', backgroundColor: '#375e38' }} align="right"><strong>Mid-Year</strong></TableCell>
                      <TableCell sx={{ color: 'white', backgroundColor: '#375e38' }} align="right"><strong>Year-End</strong></TableCell>
                      <TableCell sx={{ color: 'white', backgroundColor: '#375e38' }} align="right"><strong>Total</strong></TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {viewData.map((personnel, index) => (
                      <TableRow 
                        key={index}
                        sx={{ 
                          '&:nth-of-type(odd)': { backgroundColor: '#f9f9f9' },
                          '&:hover': { backgroundColor: '#f0f0f0' }
                        }}
                      >
                        <TableCell>{personnel.positionTitle}</TableCell>
                        <TableCell>{personnel.gradelevel_SG}</TableCell>
                        <TableCell>{personnel.step}</TableCell>
                        <TableCell>{personnel.employeeFullName}</TableCell>
                        <TableCell>{personnel.division}</TableCell>
                        <TableCell align="right">{formatCurrency(parseFloat(personnel.monthlySalary))}</TableCell>
                        <TableCell align="right">{formatCurrency(parseFloat(personnel.annualSalary))}</TableCell>
                        <TableCell align="right">{formatCurrency(parseFloat(personnel.RATA))}</TableCell>
                        <TableCell align="right">{formatCurrency(parseFloat(personnel.PERA))}</TableCell>
                        <TableCell align="right">{formatCurrency(parseFloat(personnel.uniformALLOWANCE))}</TableCell>
                        <TableCell align="right">{formatCurrency(parseFloat(personnel.midyearBonus))}</TableCell>
                        <TableCell align="right">{formatCurrency(parseFloat(personnel.yearEndBonus))}</TableCell>
                        <TableCell align="right">{formatCurrency(parseFloat(personnel.Total))}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center', mb: 2 }}>
                <Typography variant="body1" fontWeight="bold" mr={2}>
                  GRAND TOTAL:
                </Typography>
                <Typography variant="body1" fontWeight="bold">
                  {formatCurrency(grandTotal)}
                </Typography>
              </Box>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseViewDialog} color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>

      <ToastContainer />

      <CustomTable
        ROWS_PER_PAGE={ROWS_PER_PAGE}
        dataListName={dataListName}
        apiPath={apiPath}
        rows={filteredRows}
        columns={useMemo(() => Object.keys(schema)
          .filter((key) => schema[key].show === true || key === "action")
          .map((key) => {
            const fieldSchema = schema[key];
            const column = {
              field: key,
              label: fieldSchema.label,
              type: fieldSchema.type,
              searchable: fieldSchema.searchable || false,
              textAlign: fieldSchema.alignRight ? "right" : "left",
            };

            if (fieldSchema.type === "action") {
              column.render = (row) => (
                <CustomMenu
                  additionalMenuOptions={menuOptions}
                  customEditElement={customEditElement}
                  hasEdit={hasEdit}
                  hasDelete={hasDelete}
                  row={row}
                  schema={schema}
                  endpoint={apiPath}
                  dataListName={dataListName}
                  disableEdit={row.status === "Submitted" || row.status === "Approved"}
                  disableDelete={row.status === "Submitted" || row.status === "Approved"}
                />
              );
            }

            if (fieldSchema.customRender) {
              column.render = (row) => fieldSchema.customRender(row);
            }

            return column;
          }), [schema, menuOptions, customEditElement, hasEdit, hasDelete, apiPath, dataListName])}
      />
    </>
  );
};

PSCasualCustomPageTable.propTypes = {
  dataListName: PropTypes.string.isRequired,
  schema: PropTypes.objectOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      type: PropTypes.string.isRequired,
      show: PropTypes.bool,
      searchable: PropTypes.bool,
      customRender: PropTypes.func,
      default: PropTypes.any,
      alignRight: PropTypes.bool,
    })
  ).isRequired,
  title: PropTypes.string,
  description: PropTypes.string,
  searchable: PropTypes.bool,
  hasEdit: PropTypes.bool,
  hasDelete: PropTypes.bool,
  hasAdd: PropTypes.bool,
  customAddElement: PropTypes.element,
  customEditElement: PropTypes.element,
  additionalMenuOptions: PropTypes.array,
  ROWS_PER_PAGE: PropTypes.number,
};

export default PSCasualCustomPageTable;
